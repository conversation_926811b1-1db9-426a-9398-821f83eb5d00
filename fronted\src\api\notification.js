import request from '@/utils/request'
import sseManager from '@/utils/sse'
import { getApiBaseUrl } from '@/utils/serverConfig'

/**
 * 通知相关API
 */

// 获取通知列表
export function getNotificationList(params) {
  return request({
    url: '/notification/list',
    method: 'get',
    params
  })
}

// 获取通知统计
export function getNotificationStats() {
  return request({
    url: '/notification/stats',
    method: 'get'
  })
}

// 标记通知为已读
export function markAsRead(data) {
  return request({
    url: '/notification/read',
    method: 'post',
    data
  })
}

// 标记所有通知为已读
export function markAllAsRead(data = {}) {
  return request({
    url: '/notification/read-all',
    method: 'post',
    data
  })
}

// 删除通知
export function deleteNotifications(data) {
  return request({
    url: '/notification/delete',
    method: 'delete',
    data
  })
}

// 清理旧通知
export function cleanupNotifications(data) {
  return request({
    url: '//notification/cleanup',
    method: 'post',
    data
  })
}

// 获取通知设置
export function getNotificationSettings() {
  return request({
    url: '/notification/settings',
    method: 'get'
  })
}

// 更新通知设置
export function updateNotificationSettings(data) {
  return request({
    url: '/notification/settings',
    method: 'put',
    data
  })
}

/**
 * 🚀 创建通知SSE连接
 * @param {Object} callbacks 回调函数
 * @returns {EventSource} SSE连接实例
 */
export function createNotificationSSE(callbacks = {}) {
  console.log('🚀 开始创建通知SSE连接...')

  const {
    onNotification = null,
    onOpen = null,
    onError = null,
    onClose = null
  } = callbacks
  let baseUrl = getApiBaseUrl()

  const token = localStorage.getItem('token')
  const jwt = encodeURIComponent(token || '')
  const sseUrl = `${baseUrl}/notification/sse?token=${jwt}`

  console.log('📡 通知SSE连接URL:', sseUrl)
  console.log('🔑 Token存在:', !!token)

  return sseManager.connect(sseUrl, {
    onOpen: (event) => {
      console.log('✅ 通知SSE连接已建立', event)
      if (onOpen) onOpen(event)
    },
    onError: (event) => {
      console.error('❌ 通知SSE连接错误:', event)
      if (onError) onError(event)
    },
    onClose: (event) => {
      console.log('🔌 通知SSE连接已关闭', event)
      if (onClose) onClose(event)
    },
    events: {
      // 连接成功事件
      connected: (data, event) => {
        console.log('📡 通知SSE连接确认:', data, event)
      },
      // 心跳事件 - 修复：监听后端发送的ping事件
      ping: (data, event) => {
        console.log('💓 通知SSE心跳(ping):', data)
      },
      // 兼容性：也监听heartbeat事件
      heartbeat: (data, event) => {
        console.log('💓 通知SSE心跳(heartbeat):', data)
      },
      // 通知事件
      notification: (data, event) => {
        console.log('🔔 收到通知事件:', data, event)
        console.log('🔍 通知事件数据结构:', JSON.stringify(data, null, 2))
        if (onNotification) {
          console.log('📤 调用通知回调函数')
          // 🚨 修复：根据实际数据结构传递正确的数据
          // 后端发送的数据格式：{ data: { type, action, notification, stats, timestamp } }
          onNotification(data.data || data)
        } else {
          console.warn('⚠️ 没有设置通知回调函数')
        }
      }
    },
    reconnect: true, // 重新启用自动重连
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
  })
}

/**
 * 断开通知SSE连接 - 模仿dashboard实现
 */
export function disconnectNotificationSSE() {
  const baseUrl = getApiBaseUrl()
  const sseUrl = `${baseUrl}/notification/sse`
  sseManager.disconnect(sseUrl)
}
